边缘集群AI推理的分布式任务调度
 时间限制：30s 空间限制：512MB

背景信息

在多用户共享算力资源的场景下，由于推理任务的启动时间不同步、性能需求差异显著，加之服务器本 身的异构性，系统会面临复杂的调度需求。
需要选手设计调度算法，尽可能满足多个用户的吞吐量需求。

题目描述

多个用户在不同时刻有不同的推理请求，为处理请求提供多种服务器。选手需要通过把请求放置于服务 器上，并采用迁移操作使得尽可能满足吞吐量需求。


以下是相关信息的详细描述：
. 服务器：提供N台服务器，第i台服务器有gi个NP U，显存大小是mi





MB。单个服务器的所有

NP U是相同的。
. 推理耗时：第i种服务器的单个NP U同时处理多个请求时，该NP U上的第j个请求的耗时是
⌈ ⌉毫秒，其中f (Bj ) = ki ∗ Bj 表示推理速度，ki表示该NP U的推理速度系数，Bj表示 第j个请求的batchsize。请求在第x毫秒开始推理，推理耗时y毫秒，则该请求在第x + y毫秒完 成推理。不考虑模型加载耗时。
· 显存：Memory = ai ∗ batchsize + bi表示显存与batchsize的关系。ai, bi表示第i个用户使 用的模型的固有参数。
· 用户：共有M个用户有推理诉求。其中第i个用户希望在第[si, ei )毫秒内推理cnti个样本。
. 通信时延：第i个用户把请求发送到第j种服务器的通信时延是latencyj ,i毫秒，即用户在第x毫秒 发送请求，服务器在第x + latencyj ,i毫秒收到请求。用户可在第x + latencyj ,i + 1毫秒发送下 一个请求。
. 迁移次数：形式化地，第i个用户在同一时刻只能发送一个请求，记接收该请求的NP U编号是v。 按照时间顺序，该用户发送第一个请求到最后一个请求形成的v序列是V。记V的长度是L，
 则该用户得分随movei增加而减少，具体细节见评分规则。
 推理顺序：每个NP U对应一个队列，队列内存储未完成推理的请求。队列排序的第一关键字是请 求到达该NP U所在服务器的时刻，越早到达的请求越靠近队首。对于同时到达的请求，编号越小 的用户发送的请求越靠近队首。每毫秒会处理一次队列。按照先后顺序执行的处理方式如下：

i. 移除已完成推理的请求。
ii. 增加当前时刻接收到的请求。
iii. 将序列排序。
iv. 从队首至队尾依次扫描请求，若加上该请求所需显存后未超过服务器显存，则认为本毫秒对该 请求分配推理资源。
备注：本题的最小时间单位是毫秒，不可拆分。

题目交互

输入
第一行一个整数N(1 ≤ N ≤ 10)表示服务器种类数。
接着N行，每行3个整数gi, ki, mi (1 ≤ gi ≤ 10, 1 ≤ ki ≤ 5, 1000 ≤ mi ≤ 2000)，其中gi表示第i 台服务器NP U个数，ki表示推理速度参数， mi表示NP U显存大小。与上文“服务器”含义一致。
接着一行，该行一个整数M(1 ≤ M ≤ 500)表示用户数量。
接着M行，每行3个整数si, ei, cnti (0 ≤ si < ei ≤ 60000, 1 ≤ cnti ≤ 6000, 5 × cnti ≤ ei − si )，其中si, ei表示用户的推理请求时间段[si, ei ) ，cnti表示待推理样本数量。
接着N行，每行M个整数，其中第i行第j个整数latencyi,j (10 ≤ latencyi,j ≤ 20)表示第j个用户把 请求发送到第i种服务器的通信时延。
接着M行，每行2个整数ai, bi (10 ≤ ai ≤ 20, 100 ≤ bi ≤ 200)表示第i个用户使用的模型的显存和 batchsize的关系。
输出
输出2M行，第2i − 1行和第2i行表示第i个用户的推理方案。
第2i − 1行包含一个整数Ti，需要保证1 ≤ Ti ≤ 300。
第2i行包含4Ti个整数，第4j − 3个整数timej (0 ≤ timej < timej+1 ≤ 1000000, timeTi +1 = +∞)，第4j − 2个整数serverj (1 ≤ serverj ≤ N)，第4j − 1个整数NP Uj (1 ≤ NP Uj ≤ gi ) ,第4j个整数Bj (1 ≤ Bj ≤ 1000), a × Bj + b ≤ mserverj ，表示第i个用户在timej时刻将包含 Bj个推理样本的请求发送给第serverj台服务器的第NP Uj个NP U。选手需保证time1 ≥


评分规则
单个测试用例得分Score = h(K) × 1 × p(movei ) × 10000，其中endi表示该用
户的最后一个样本推理完成的时刻，K = 1 [endi > ei] ，h(x) = 2 − 10 ，
选手总得分为所有测试用例得分之和。

样例

输入
2
2 3 10000
6 5 20000
3
0 60000 3000
10000 50000 2000
0 200000 5000
10 20 20
20 10 12
10 100

输出
1
0 1 1 3000
2
0 1 1 1000 20000 1 2 1000
2
0 2 1 1000 100000 1 2 2000

备注：输入输出仅为说明格式。

错误类型

基础错误类型
1. 代码编译错误
2. 程序异常退出 (可能原因: 运行错误，使用异常权限，输出参数比实际多，输出参数格式不对， etc...)
3. 超出时间限制 (可能原因: 交互时未使用清空流缓存命令，程序运行超时，输出参数比实际少， etc...)
4. 超出内存限制
逻辑错误类型
. "Unknown Error" (出现时请联系大赛方)
· "RE" (程序内存访问越界或异常退出)
· "Invalid Output"（Ti或者timej违反约束）
· "Batchsize Exceeds Memory"（Bj违反约束）
. "Samples Not Fully Processed"（样本未全部发送）
· "Invalid Time Order"（timej非递增）
· "Invalid NPU Index"（ NPU编号违反约束）
· "Invalid User Send Time"（样本发送时刻早于允许发送的时刻）
. "Invalid Server Index"（服务器编号违反约束）