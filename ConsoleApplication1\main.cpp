#include <iostream>
#include <vector>
#include <queue>
#include <cmath>
#include <algorithm>
#include <numeric>
#include <tuple>
#include <climits> // For INT_MAX

using namespace std;

// --- Data Models ---

struct Server {
    int id;
    int g; // NPU count
    int k; // Speed coefficient
    int m; // Memory size
};

struct NPU {
    int id;
    int server_id;
    int k;
    int m;
    // List of (time, memory_delta) events, kept sorted by time
    vector<pair<int, int>> events;

    void add_task(int start_time, int finish_time, int mem_needed) {
        auto it_start = lower_bound(events.begin(), events.end(), make_pair(start_time, INT_MIN));
        events.insert(it_start, { start_time, mem_needed });

        auto it_finish = lower_bound(events.begin(), events.end(), make_pair(finish_time, INT_MIN));
        events.insert(it_finish, { finish_time, -mem_needed });
    }

    int find_earliest_start_time(int arrival_time, int mem_needed) const {
        if (mem_needed > m) {
            return -1;
        }

        int current_mem_usage = 0;
        for (const auto& event : events) {
            if (event.first <= arrival_time) {
                current_mem_usage += event.second;
            }
            else {
                break;
            }
        }

        if (current_mem_usage + mem_needed <= m) {
            return arrival_time;
        }

        auto it = upper_bound(events.begin(), events.end(), make_pair(arrival_time, INT_MAX));

        int future_mem_usage = current_mem_usage;
        for (auto i = it; i != events.end(); ++i) {
            future_mem_usage += i->second;
            if (future_mem_usage + mem_needed <= m) {
                return i->first;
            }
        }
        return -1;
    }
};

struct User {
    int id;
    int s, e, cnt;
    long long samples_left;
    long long next_send_time;
    int last_npu_id = -1;
    vector<tuple<int, int, int, int>> requests;
};


// --- Helper Functions ---

int calculate_inference_time(int batch_size, int k) {
    if (batch_size <= 0) return 0;
    int sqrt_b = static_cast<int>(sqrt(batch_size));
    if (sqrt_b * sqrt_b < batch_size) {
        sqrt_b++;
    }
    return (sqrt_b + k - 1) / k;
}

int find_optimal_batch_size(int max_batch_mem, int samples_left, int k, long long remaining_time) {
    if (remaining_time <= 0) {
        return 1;
    }

    int low = 1;
    int high = min(max_batch_mem, (int)samples_left);
    int best_batch = 1;

    while (low <= high) {
        int mid = low + (high - low) / 2;
        if (mid == 0) {
            low = 1;
            continue;
        }
        if (calculate_inference_time(mid, k) <= remaining_time) {
            best_batch = mid;
            low = mid + 1;
        }
        else {
            high = mid - 1;
        }
    }
    return best_batch;
}


// --- Main Logic ---

int main() {
    ios_base::sync_with_stdio(false);
    cin.tie(NULL);

    int N;
    cin >> N;
    if (cin.eof()) return 0;

    vector<Server> servers_data(N);
    for (int i = 0; i < N; ++i) {
        servers_data[i].id = i;
        cin >> servers_data[i].g >> servers_data[i].k >> servers_data[i].m;
    }

    int M;
    cin >> M;
    vector<User> users(M);
    for (int i = 0; i < M; ++i) {
        users[i].id = i;
        cin >> users[i].s >> users[i].e >> users[i].cnt;
        users[i].samples_left = users[i].cnt;
        users[i].next_send_time = users[i].s;
    }

    vector<vector<int>> latencies(N, vector<int>(M));
    for (int i = 0; i < N; ++i) {
        for (int j = 0; j < M; ++j) {
            cin >> latencies[i][j];
        }
    }

    int A, B;
    cin >> A >> B;

    vector<NPU> npus;
    int npu_counter = 0;
    for (const auto& server : servers_data) {
        for (int i = 0; i < server.g; ++i) {
            npus.push_back({ npu_counter++, server.id, server.k, server.m, {} });
        }
    }

    priority_queue<pair<long long, int>, vector<pair<long long, int>>, greater<pair<long long, int>>> user_pq;
    for (const auto& user : users) {
        user_pq.push({ user.next_send_time, user.id });
    }

    while (!user_pq.empty()) {
        auto top = user_pq.top();
        user_pq.pop();
        long long time = top.first;
        int user_id = top.second;
        User& user = users[user_id];

        if (user.samples_left <= 0) continue;

        long long send_time = max(time, user.next_send_time);

        tuple<int, int, int> best_cost = { 1, INT_MAX, 0 };
        int best_finish_time = -1;
        int best_npu_idx = -1;
        int best_batch_size = -1;
        int best_latency = -1;

        for (int i = 0; i < npus.size(); ++i) {
            const auto& npu = npus[i];
            const auto& server = servers_data[npu.server_id];

            if (server.m <= B) continue;
            int max_b_for_npu = (server.m - B) / A;
            if (max_b_for_npu <= 0) continue;

            int latency = latencies[server.id][user.id];
            int arrival_time = send_time + latency;

            long long time_for_inference = max(0LL, (long long)user.e - arrival_time);

            int good_batch = find_optimal_batch_size(max_b_for_npu, user.samples_left, server.k, time_for_inference);
            int mem_needed = A * good_batch + B;

            int start_time = npu.find_earliest_start_time(arrival_time, mem_needed);
            if (start_time == -1) continue;

            int inference_time = calculate_inference_time(good_batch, server.k);
            int finish_time = start_time + inference_time;

            int has_move = (user.last_npu_id != -1 && npu.id != user.last_npu_id);
            bool is_late = finish_time > user.e;

            tuple<int, int, int> current_cost = { is_late, finish_time, has_move };

            if (current_cost < best_cost) {
                best_cost = current_cost;
                best_finish_time = finish_time;
                best_npu_idx = i;
                best_batch_size = good_batch;
                best_latency = latency;
            }
        }

        if (best_npu_idx != -1) {
            NPU& chosen_npu = npus[best_npu_idx];
            int batch = best_batch_size;

            int mem_needed = A * batch + B;
            int server_k = servers_data[chosen_npu.server_id].k;
            int inference_time = calculate_inference_time(batch, server_k);
            int start_time = best_finish_time - inference_time;

            chosen_npu.add_task(start_time, best_finish_time, mem_needed);

            int npu_id_in_server;
            int base_npu_id = 0;
            for (int i = 0; i < chosen_npu.server_id; ++i) {
                base_npu_id += servers_data[i].g;
            }
            npu_id_in_server = chosen_npu.id - base_npu_id + 1;

            user.requests.emplace_back(send_time, chosen_npu.server_id + 1, npu_id_in_server, batch);

            user.samples_left -= batch;
            // ***** THIS IS THE FIX *****
            // The next send time must account for the communication latency of the request just sent.
            user.next_send_time = send_time + best_latency + 1;
            user.last_npu_id = chosen_npu.id;

            if (user.samples_left > 0) {
                user_pq.push({ user.next_send_time, user.id });
            }
        }
        else {
            if (user.samples_left > 0) {
                user.next_send_time = time + 1;
                user_pq.push({ user.next_send_time, user.id });
            }
        }
    }

    for (auto& user : users) {
        cout << user.requests.size() << "\n";
        bool first = true;
        for (const auto& req : user.requests) {
            if (!first) {
                cout << " ";
            }
            cout << get<0>(req) << " " << get<1>(req) << " "
                << get<2>(req) << " " << get<3>(req);
            first = false;
        }
        cout << "\n";
    }

    return 0;
}