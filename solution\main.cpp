#include <iostream>
#include <vector>
#include <cmath>
#include <algorithm>
#include <climits>
using namespace std;

struct Server {
    int g;   // NPU个数
    int k;   // 推理速度系数
    int m;   // 显存大小（MB）
};

struct User {
    int s;     // 起始时间
    int e;     // 结束时间
    int cnt;   // 样本数量
    int a;     // 显存参数a
    int b;     // 显存参数b
};

struct Request {
    int send_time;
    int server_id;
    int npu_id;
    int batchsize;
};

int main() {
    // 读取服务器种类数
    int N;
    cin >> N;
    vector<Server> servers(N);
    for (int i = 0; i < N; i++) {
        cin >> servers[i].g >> servers[i].k >> servers[i].m;
    }

    // 读取用户数量
    int M;
    cin >> M;
    vector<User> users(M);
    for (int i = 0; i < M; i++) {
        cin >> users[i].s >> users[i].e >> users[i].cnt;
    }

    // 读取通信时延矩阵
    vector<vector<int>> latencies(N, vector<int>(M));
    for (int i = 0; i < N; i++) {
        for (int j = 0; j < M; j++) {
            cin >> latencies[i][j];
        }
    }

    // 读取用户模型参数
    for (int i = 0; i < M; i++) {
        cin >> users[i].a >> users[i].b;
    }

    // 初始化NPU负载
    vector<vector<double>> npu_load(N);
    for (int i = 0; i < N; i++) {
        npu_load[i] = vector<double>(servers[i].g, 0.0);
    }

    // 存储每个用户的请求计划
    vector<vector<Request>> user_requests(M);

    // 为每个用户生成请求计划
    for (int i = 0; i < M; i++) {
        int selected_server = -1;
        vector<int> candidate_servers;
        vector<int> valid_servers;

        // 筛选有效服务器
        for (int j = 0; j < N; j++) {
            // 计算最大batchsize（考虑显存约束）
            int max_batch = (servers[j].m - users[i].b) / users[i].a;
            if (max_batch < 1) continue; // 无法处理任何样本
            
            valid_servers.push_back(j);
            max_batch = min(max_batch, 1000);
            
            // 计算最小请求次数
            int T_min = (users[i].cnt + max_batch - 1) / max_batch;
            // 计算最大请求次数（考虑时间窗口和通信时延）
            int T_max = 1 + (users[i].e - users[i].s) / latencies[j][i];
            
            if (T_min <= T_max) {
                candidate_servers.push_back(j);
            }
        }

        // 选择最优服务器
        if (!candidate_servers.empty()) {
            double max_k = -1.0;
            int min_T_min = INT_MAX;
            for (int j : candidate_servers) {
                int max_batch = min(1000, (servers[j].m - users[i].b) / users[i].a);
                int T_min_val = (users[i].cnt + max_batch - 1) / max_batch;
                if (servers[j].k > max_k || 
                    (servers[j].k == max_k && T_min_val < min_T_min)) {
                    max_k = servers[j].k;
                    selected_server = j;
                    min_T_min = T_min_val;
                }
            }
        } 
        else if (!valid_servers.empty()) {
            // 选择请求次数最少的服务器
            int min_T_min = INT_MAX;
            for (int j : valid_servers) {
                int max_batch = min(1000, (servers[j].m - users[i].b) / users[i].a);
                int T_min_val = (users[i].cnt + max_batch - 1) / max_batch;
                if (T_min_val < min_T_min) {
                    min_T_min = T_min_val;
                    selected_server = j;
                }
            }
        }
        else {
            // 选择显存最大的服务器作为保底
            int max_m = -1;
            for (int j = 0; j < N; j++) {
                if (servers[j].m > max_m) {
                    max_m = servers[j].m;
                    selected_server = j;
                }
            }
        }

        // 计算请求次数和batchsize
        int j0 = selected_server;
        int max_batch = (servers[j0].m - users[i].b) / users[i].a;
        if (max_batch < 1) max_batch = 1; // 至少处理1个样本
        max_batch = min(max_batch, 1000);
        
        int T_i = (users[i].cnt + max_batch - 1) / max_batch; // 向上取整
        vector<int> batchsizes(T_i, max_batch);
        
        // 调整最后一个batchsize
        int remaining = users[i].cnt - max_batch * (T_i - 1);
        if (remaining > 0) {
            batchsizes[T_i - 1] = remaining;
        }

        // 选择负载最小的NPU
        int selected_npu = -1;
        double min_load = 1e18;
        for (int k = 0; k < servers[j0].g; k++) {
            if (npu_load[j0][k] < min_load) {
                min_load = npu_load[j0][k];
                selected_npu = k;
            }
        }

        // 更新NPU负载
        double added_load = 0.0;
        for (int bs : batchsizes) {
            added_load += sqrt(bs);
        }
        npu_load[j0][selected_npu] += added_load;

        // 生成请求（确保发送时间≥s_i）
        int latency_val = latencies[j0][i];
        int current_time = users[i].s; // 从起始时间开始
        for (int t = 0; t < T_i; t++) {
            Request req;
            req.send_time = current_time;
            req.server_id = j0;
            req.npu_id = selected_npu;
            req.batchsize = batchsizes[t];
            user_requests[i].push_back(req);
            
            // 更新时间（确保满足通信时延要求）
            current_time += latency_val;
        }
    }

    // 输出请求计划
    for (int i = 0; i < M; i++) {
        cout << user_requests[i].size() << endl;
        for (int j = 0; j < user_requests[i].size(); j++) {
            Request& req = user_requests[i][j];
            if (j > 0) cout << " ";
            cout << req.send_time << " " << req.server_id + 1 << " " 
                 << req.npu_id + 1 << " " << req.batchsize;
        }
        cout << endl;
    }

    return 0;
}