#include <iostream>
#include <vector>
#include <queue>
#include <cmath>
#include <algorithm>
#include <numeric>
#include <tuple>
#include <climits> // For INT_MAX
#include <limits>
#include <set>
#include <unordered_map>

using namespace std;

// 动态迁移惩罚系统 - 根据系统负载和用户紧急程度调整
const int BASE_MOVE_PENALTY = 50; // 降低基础迁移惩罚
const double URGENCY_THRESHOLD = 0.8; // 紧急度阈值

// --- Data Models ---

struct Server {
    int id;
    int g; // NPU count
    int k; // Speed coefficient
    int m; // Memory size
};

struct NPU {
    int id;
    int server_id;
    int k;
    int m;
    vector<pair<int, int>> events;
    double load_factor = 0.0; // 负载因子，用于动态调整策略

    void add_task(int start_time, int finish_time, int mem_needed) {
        auto it_start = lower_bound(events.begin(), events.end(), make_pair(start_time, INT_MIN));
        events.insert(it_start, {start_time, mem_needed});

        auto it_finish = lower_bound(events.begin(), events.end(), make_pair(finish_time, INT_MIN));
        events.insert(it_finish, {finish_time, -mem_needed});
        
        // 更新负载因子
        update_load_factor();
    }

    void update_load_factor() {
        if (events.empty()) {
            load_factor = 0.0;
            return;
        }
        
        int total_usage = 0;
        int time_span = 0;
        if (events.size() >= 2) {
            time_span = events.back().first - events.front().first;
            if (time_span > 0) {
                // 计算平均内存使用率
                int current_mem = 0;
                for (const auto& event : events) {
                    current_mem += event.second;
                    total_usage += current_mem;
                }
                load_factor = (double)total_usage / (time_span * m);
            }
        }
    }

    int find_earliest_start_time(int arrival_time, int mem_needed) const {
        if (mem_needed > m) {
            return -1;
        }

        int current_mem_usage = 0;
        for (const auto& event : events) {
            if (event.first <= arrival_time) {
                current_mem_usage += event.second;
            } else {
                break;
            }
        }

        if (current_mem_usage + mem_needed <= m) {
            return arrival_time;
        }

        auto it = upper_bound(events.begin(), events.end(), make_pair(arrival_time, INT_MAX));
        for (auto i = it; i != events.end(); ++i) {
            current_mem_usage += i->second;
            if (current_mem_usage + mem_needed <= m) {
                return i->first;
            }
        }
        return -1;
    }
};

struct User {
    int id;
    int s, e, cnt;
    long long samples_left;
    long long next_send_time;
    int last_npu_id = -1;
    vector<tuple<int, int, int, int>> requests;
    int requests_made = 0;
    
    // 新增字段用于优化
    double completion_ratio = 0.0;
    double urgency_factor = 1.0;
    int consecutive_migrations = 0; // 连续迁移次数
    vector<int> preferred_npus; // 偏好的NPU列表
};

// --- 优化的帮助函数 ---

// 计算推理时间 - 使用更精确的计算
int calculate_inference_time(int batch_size, int k) {
    if (batch_size <= 0) return 0;
    double sqrt_batch = sqrt(batch_size);
    return static_cast<int>(ceil(sqrt_batch / k));
}

// 智能批量大小优化 - 寻找最优"甜点"
int find_optimal_batch_size(int min_batch, int max_batch, long long samples_left, int k, 
                           long long remaining_time, int available_memory, int A, int B) {
    
    int limit = min(max_batch, (int)samples_left);
    if (limit <= 0) return 1;

    // 计算显存约束下的最大批量
    int memory_limit = (available_memory - B) / A;
    limit = min(limit, memory_limit);
    
    if (limit < min_batch) {
        min_batch = max(1, limit);
    }

    // 生成智能候选批量大小
    vector<int> candidates;
    
    // 1. 基础候选
    candidates.push_back(min_batch);
    candidates.push_back(limit);
    
    // 2. 基于推理效率的最优点（利用√B/k的非线性特性）
    for (int batch = min_batch; batch <= limit; batch++) {
        int inference_time = calculate_inference_time(batch, k);
        if (inference_time == 0) continue;
        
        double efficiency = (double)batch / inference_time;
        
        // 寻找效率峰值点
        if (batch > min_batch && batch < limit) {
            int prev_time = calculate_inference_time(batch - 1, k);
            int next_time = calculate_inference_time(batch + 1, k);
            
            if (prev_time > 0 && next_time > 0) {
                double prev_eff = (double)(batch - 1) / prev_time;
                double next_eff = (double)(batch + 1) / next_time;
                
                // 如果当前点是局部最优，添加到候选
                if (efficiency >= prev_eff && efficiency >= next_eff) {
                    candidates.push_back(batch);
                }
            }
        }
    }
    
    // 3. 基于时间约束的候选
    if (remaining_time > 0) {
        for (int k_factor = 1; k_factor <= 5; k_factor++) {
            int target_time = remaining_time / k_factor;
            if (target_time <= 0) continue;
            
            // 反推批量大小：time = ⌈√B/k⌉ => B ≈ (time * k)²
            int estimated_batch = (target_time * k) * (target_time * k);
            estimated_batch = max(min_batch, min(estimated_batch, limit));
            candidates.push_back(estimated_batch);
        }
    }
    
    // 4. 几何级数候选
    for (int batch = min_batch; batch <= limit; batch *= 2) {
        candidates.push_back(batch);
    }
    
    // 5. 显存利用率优化候选
    for (double utilization = 0.7; utilization <= 1.0; utilization += 0.1) {
        int batch = (int)((available_memory * utilization - B) / A);
        if (batch >= min_batch && batch <= limit) {
            candidates.push_back(batch);
        }
    }

    // 去重和排序
    sort(candidates.begin(), candidates.end());
    candidates.erase(unique(candidates.begin(), candidates.end()), candidates.end());

    // 评估所有候选并选择最优
    int best_batch = min_batch;
    double best_score = -1;

    for (int batch : candidates) {
        if (batch <= 0 || batch > limit || batch < min_batch) continue;

        int inference_time = calculate_inference_time(batch, k);
        if (inference_time <= 0) continue;

        // 多维度评分函数
        double efficiency = (double)batch / inference_time; // 吞吐效率
        double memory_utilization = (double)(A * batch + B) / available_memory; // 显存利用率
        double time_fitness = 1.0; // 时间适应度
        
        if (remaining_time > 0) {
            if (inference_time > remaining_time) {
                time_fitness = 0.2; // 超时重度惩罚
            } else {
                // 奖励充分利用时间的批量
                time_fitness = 1.0 + (1.0 - (double)inference_time / remaining_time) * 0.3;
            }
        }

        // 综合评分：优先考虑效率，兼顾显存利用率和时间适应度
        double score = efficiency * 2.0 + memory_utilization * 1.0 + time_fitness * 1.5;
        
        // 对较大批量给予小幅奖励（提升吞吐量）
        score += (double)batch / limit * 0.2;

        if (score > best_score) {
            best_score = score;
            best_batch = batch;
        }
    }

    return best_batch;
}

// 多维度优先级计算
double compute_advanced_priority(const User& user, long long current_time) {
    long long time_left = max(1LL, (long long)user.e - user.next_send_time);
    long long total_time = user.e - user.s;
    
    // 1. 基础紧急度：样本密度
    double base_urgency = (double)user.samples_left / time_left;
    
    // 2. 进度因子：已完成的比例
    double progress = 1.0 - (double)user.samples_left / user.cnt;
    double progress_factor = 1.0 + progress * 0.5; // 越接近完成，优先级越高
    
    // 3. 时间压力因子
    double time_pressure = 1.0;
    if (total_time > 0) {
        double elapsed_ratio = (double)(current_time - user.s) / total_time;
        double completion_ratio = progress;
        
        // 如果进度落后于时间进度，增加优先级
        if (completion_ratio < elapsed_ratio) {
            time_pressure = 1.0 + (elapsed_ratio - completion_ratio) * 2.0;
        }
    }
    
    // 4. 截止时间临近因子
    double deadline_factor = 1.0;
    if (time_left < total_time * 0.2) { // 剩余时间不足20%
        deadline_factor = 2.0 + (1.0 - (double)time_left / (total_time * 0.2)) * 3.0;
    }
    
    // 5. 迁移惩罚衰减因子（连续迁移次数越多，惩罚越小）
    double migration_tolerance = 1.0 + min(user.consecutive_migrations * 0.1, 0.5);
    
    return base_urgency * progress_factor * time_pressure * deadline_factor * migration_tolerance;
}

// 动态迁移惩罚计算
int calculate_dynamic_migration_penalty(const User& user, const NPU& npu, long long current_time) {
    double urgency = compute_advanced_priority(user, current_time);
    double system_load = npu.load_factor;
    
    // 基础惩罚
    int penalty = BASE_MOVE_PENALTY;
    
    // 根据紧急度调整
    if (urgency > URGENCY_THRESHOLD) {
        penalty = (int)(penalty * (1.0 - urgency)); // 高紧急度降低迁移惩罚
    }
    
    // 根据系统负载调整
    if (system_load < 0.5) {
        penalty = (int)(penalty * 0.7); // 低负载时降低迁移惩罚
    } else if (system_load > 0.8) {
        penalty = (int)(penalty * 1.3); // 高负载时增加迁移惩罚
    }
    
    // 连续迁移衰减
    if (user.consecutive_migrations > 2) {
        penalty = (int)(penalty * 0.8); // 如果已经迁移多次，进一步降低惩罚
    }
    
    return max(10, penalty); // 最小惩罚值
}

// --- Main Logic ---

int main() {
    // Fast I/O
    ios_base::sync_with_stdio(false);
    cin.tie(NULL);

    // 1. Read Input
    int N;
    cin >> N;
    if (cin.eof()) return 0;

    vector<Server> servers_data(N);
    for (int i = 0; i < N; ++i) {
        servers_data[i].id = i;
        cin >> servers_data[i].g >> servers_data[i].k >> servers_data[i].m;
    }

    int M;
    cin >> M;
    vector<User> users(M);
    for (int i = 0; i < M; ++i) {
        users[i].id = i;
        cin >> users[i].s >> users[i].e >> users[i].cnt;
        users[i].samples_left = users[i].cnt;
        users[i].next_send_time = users[i].s;
    }

    vector<vector<int>> latencies(N, vector<int>(M));
    for (int i = 0; i < N; ++i) {
        for (int j = 0; j < M; ++j) {
            cin >> latencies[i][j];
        }
    }

    int A, B;
    cin >> A >> B;

    // 2. Initialize Models
    vector<NPU> npus;
    int npu_counter = 0;
    for (const auto& server : servers_data) {
        for (int i = 0; i < server.g; ++i) {
            npus.push_back({npu_counter++, server.id, server.k, server.m, {}, 0.0});
        }
    }

    // 3. 高级事件驱动调度系统
    priority_queue<pair<double, int>> user_pq;
    long long global_time = 0;
    
    // 初始化用户优先级队列
    for (const auto& user : users) {
        double priority = compute_advanced_priority(user, global_time);
        user_pq.push({priority, user.id});
    }

    // 主调度循环
    while (!user_pq.empty()) {
        auto top = user_pq.top();
        user_pq.pop();
        int user_id = top.second;
        User& user = users[user_id];

        if (user.samples_left <= 0) continue;

        global_time = max(global_time, user.next_send_time);
        long long send_time = user.next_send_time;

        // 更新用户状态
        user.completion_ratio = 1.0 - (double)user.samples_left / user.cnt;
        user.urgency_factor = compute_advanced_priority(user, global_time);

        // --- 智能资源分配决策 ---
        struct CandidateOption {
            int npu_idx;
            int batch_size;
            int finish_time;
            double cost;
            bool meets_deadline;
        };
        
        vector<CandidateOption> candidates;
        
        // 计算剩余时间和请求配额
        long long remaining_time = max(0LL, (long long)user.e - send_time);
        int remaining_requests = max(1, 300 - user.requests_made);
        int min_batch_requirement = (int)((user.samples_left + remaining_requests - 1) / remaining_requests);

        // 遍历所有NPU寻找最优方案
        for (int i = 0; i < npus.size(); ++i) {
            NPU& npu = npus[i];
            const Server& server = servers_data[npu.server_id];

            // 预筛选：检查基本约束
            if (server.m <= B) continue;
            int max_possible_batch = (server.m - B) / A;
            if (max_possible_batch <= 0) continue;

            int latency = latencies[server.id][user.id];
            int arrival_time = send_time + latency;

            // 使用智能批量大小优化
            int optimal_batch = find_optimal_batch_size(
                min_batch_requirement, max_possible_batch, user.samples_left, 
                server.k, remaining_time, server.m, A, B
            );
            
            int mem_needed = A * optimal_batch + B;
            int start_time = npu.find_earliest_start_time(arrival_time, mem_needed);
            if (start_time == -1) continue;

            int inference_time = calculate_inference_time(optimal_batch, server.k);
            int finish_time = start_time + inference_time;

            // 若预计超出用户截止时间，尝试缩小 batch 以赶上截止
            if (finish_time > user.e) {
                long long time_left_slot = (long long)user.e - start_time;
                if (time_left_slot <= 0) {
                    continue; // 没有可用时间
                }
                long long max_batch_by_time = (long long)time_left_slot * server.k;
                max_batch_by_time = max_batch_by_time * max_batch_by_time; // (t·k)^2
                int capped_batch = (int)min<long long>(optimal_batch, max_batch_by_time);
                if (capped_batch < min_batch_requirement) {
                    continue; // 无法满足最小批量
                }
                // 再次检查显存限制
                int mem_cap = (server.m - B) / A;
                capped_batch = min(capped_batch, mem_cap);
                if (capped_batch < min_batch_requirement) {
                    continue;
                }
                optimal_batch = capped_batch;
                mem_needed = A * optimal_batch + B;
                inference_time = calculate_inference_time(optimal_batch, server.k);
                finish_time = start_time + inference_time;
                if (finish_time > user.e) {
                    continue; // 仍超时则放弃该 NPU
                }
            }

            // 高级代价函数
            double base_cost = finish_time;
            
            // 1. 时间适应度
            double time_factor = 1.0;
            if (finish_time > user.e) {
                double overtime = finish_time - user.e;
                double total_time = user.e - user.s;
                time_factor = 1.0 + (overtime / total_time) * 10.0; // 超时重度惩罚
            }
            
            // 2. 动态迁移惩罚
            double migration_penalty = 0;
            if (user.last_npu_id != -1 && npu.id != user.last_npu_id) {
                migration_penalty = calculate_dynamic_migration_penalty(user, npu, global_time);
            }
            
            // 3. 效率奖励
            double efficiency_bonus = 0;
            if (inference_time > 0) {
                double throughput = (double)optimal_batch / inference_time;
                efficiency_bonus = -throughput * 2.0; // 负值表示奖励
            }
            
            // 4. 负载均衡因子
            double load_penalty = npu.load_factor * 20.0;
            
            // 5. 延迟惩罚
            double latency_penalty = latency * 0.3;
            
            // 6. 紧急度加权
            double urgency_weight = 1.0 / (1.0 + user.urgency_factor);
            
            double total_cost = (base_cost * time_factor + migration_penalty + load_penalty + latency_penalty + efficiency_bonus) * urgency_weight;
            
            candidates.push_back({
                i, optimal_batch, finish_time, total_cost, 
                finish_time <= user.e
            });
        }

        // 若没有可行的候选方案，则延迟重试
        if (candidates.empty()) {
            user.next_send_time += 1;
            user_pq.push({-user.urgency_factor, user.id});
            continue;
        }

        // 优先选择满足截止时间的方案，其次选择成本最低的
        sort(candidates.begin(), candidates.end(), [](const CandidateOption& a, const CandidateOption& b) {
            if (a.meets_deadline != b.meets_deadline) {
                return a.meets_deadline; // 优先满足截止时间的方案
            }
            return a.cost < b.cost; // 成本更低的方案
        });

        // 在满足截止时间的前提下，尽量减少迁移
        CandidateOption chosen_option = candidates[0];
        if (user.last_npu_id != -1) {
            for (const auto& cand : candidates) {
                if (cand.npu_idx == user.last_npu_id && cand.meets_deadline) {
                    // 若成本与最佳方案相差不大(≤5%)，则保持在当前 NPU，减少迁移
                    if (!chosen_option.meets_deadline || cand.cost <= chosen_option.cost * 1.05) {
                        chosen_option = cand;
                    }
                    break;
                }
            }
        }
        
        // 提交方案
        NPU& chosen_npu = npus[chosen_option.npu_idx];
        int batch_size = chosen_option.batch_size;
        int mem_needed = A * batch_size + B;
        int latency = latencies[chosen_npu.server_id][user.id];
        int start_time = chosen_option.finish_time - calculate_inference_time(batch_size, servers_data[chosen_npu.server_id].k);

        chosen_npu.add_task(start_time, chosen_option.finish_time, mem_needed);

        // 计算NPU在服务器内的编号
        int npu_id_in_server = 1;
        int base_npu_id = 0;
        for (int i = 0; i < chosen_npu.server_id; ++i) {
            base_npu_id += servers_data[i].g;
        }
        npu_id_in_server = chosen_npu.id - base_npu_id + 1;

        user.requests.emplace_back(send_time, chosen_npu.server_id + 1, npu_id_in_server, batch_size);

        // 更新用户状态
        user.samples_left -= batch_size;
        user.next_send_time = send_time + latency + 1;
        
        // 更新迁移统计
        if (user.last_npu_id != -1 && chosen_npu.id != user.last_npu_id) {
            user.consecutive_migrations++;
        } else {
            user.consecutive_migrations = 0;
        }
        user.last_npu_id = chosen_npu.id;
        
        user.requests_made++;

        // 如果还有剩余样本，重新加入队列
        if (user.samples_left > 0) {
            double new_priority = compute_advanced_priority(user, global_time);
            user_pq.push({new_priority, user.id});
        }
    }

    // 4. 输出结果
    for (auto& user : users) {
        sort(user.requests.begin(), user.requests.end());
        cout << user.requests.size() << "\n";
        bool first = true;
        for (const auto& req : user.requests) {
            if (!first) {
                cout << " ";
            }
            cout << get<0>(req) << " " << get<1>(req) << " "
                 << get<2>(req) << " " << get<3>(req);
            first = false;
        }
        cout << "\n";
    }

    return 0;
} 