通过仔细比对两个赛题文件，初赛和复赛最核心的区别在于 **AI模型的显存计算方式**，复赛对每个用户的模型引入了个性化参数，从而增加了调度的复杂性。

---

### 主要区别：模型与显存参数

最大的变化是从统一的显存模型转变为针对每个用户的个性化模型。

* **初赛** ⚙️
    [cite_start]所有用户使用 **完全相同的AI模型** [cite: 12][cite_start]。因此，一个任务请求所需显存由一个全局统一的公式计算：`Memory = a * batchsize + b` [cite: 11][cite_start]。相应地，输入数据中只包含一行，用于定义这两个全局参数 `a` 和 `b` [cite: 30]。

* **复赛** 🚀
    [cite_start]每个用户 `i` 拥有 **各自独立的模型参数** [cite: 84][cite_start]。这意味着显存计算是用户相关的：`Memory = a_i * batchsize + b_i` [cite: 84][cite_start]。为了支持这一变化，输入格式也进行了调整：不再是只有一行显存参数，而是有 `M` 行，分别为 `M` 个用户提供其专属的 `a_i` 和 `b_i` 值 [cite: 102]。

---

### 未变化的核心规则

除了显存计算的调整外，赛题的核心目标、约束和评分机制在两个阶段保持了一致。

* [cite_start]**核心目标**: 两个阶段的根本目标没有改变，都是设计一个高效的分布式任务调度算法，以尽可能满足多个用户的吞吐量需求 [cite: 5, 78]。
* [cite_start]**服务器与NPU**: 服务器的种类、每种服务器的NPU数量 `g_i` 及其推理速度系数 `k_i` 的定义保持不变 [cite: 9, 82]。
* [cite_start]**推理耗时**: 任务在NPU上的推理耗时计算公式 $\lceil\frac{B_{j}}{k_{i}*\sqrt{B_{j}}}\rceil$ 没有任何改动 [cite: 10, 83]。
* [cite_start]**评分规则**: 最终得分的计算公式，包括对任务超时完成的惩罚函数 `h(x)` 和对任务迁移次数的惩罚函数 `p(x)`，是完全相同的 [cite: 36, 108]。

总而言之，复赛通过引入用户专属的显存模型，在初赛的基础上增加了一个新的挑战维度。你的调度算法不仅要考虑服务器算力的匹配，还必须精确管理不同任务带来的异构显存负载。